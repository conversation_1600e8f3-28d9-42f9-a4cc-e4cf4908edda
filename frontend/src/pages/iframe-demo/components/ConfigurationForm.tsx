import { DemoConfig } from "../types";

interface ConfigurationFormProps {
  config: DemoConfig;
  setConfig: React.Dispatch<React.SetStateAction<DemoConfig>>;
  onGenerateToken: () => void;
  loading: boolean;
}

export const ConfigurationForm = ({ config, setConfig, onGenerateToken, loading }: ConfigurationFormProps) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Merchant ID</label>
          <input
            type="text"
            value={config.merchantId}
            onChange={(e) => setConfig((prev) => ({ ...prev, merchantId: e.target.value }))}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
            placeholder="Enter merchant ID"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Amount (cents)</label>
          <input
            type="number"
            value={config.amount}
            onChange={(e) => setConfig((prev) => ({ ...prev, amount: parseInt(e.target.value) || 0 }))}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
            placeholder="Amount in cents"
            min="1"
          />
          <p className="text-sm text-slate-500 mt-1">${(config.amount / 100).toFixed(2)}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Description</label>
          <input
            type="text"
            value={config.description}
            onChange={(e) => setConfig((prev) => ({ ...prev, description: e.target.value }))}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
            placeholder="Payment description"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">Return URL</label>
          <input
            type="url"
            value={config.returnUrl}
            onChange={(e) => setConfig((prev) => ({ ...prev, returnUrl: e.target.value }))}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
            placeholder="https://yoursite.com/success"
          />
        </div>
      </div>

      <div className="border-t border-slate-200 pt-6">
        <h3 className="text-lg font-medium text-slate-900 mb-4">Digital Wallet Options</h3>
        
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="enableGooglePay"
              checked={config.enableGooglePay}
              onChange={(e) => setConfig((prev) => ({ ...prev, enableGooglePay: e.target.checked }))}
              className="h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"
            />
            <label htmlFor="enableGooglePay" className="ml-2 block text-sm text-slate-700">
              Enable Google Pay
            </label>
          </div>

          {config.enableGooglePay && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 ml-6">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Google Pay Environment</label>
                <select
                  value={config.googlePayEnvironment}
                  onChange={(e) => setConfig((prev) => ({ ...prev, googlePayEnvironment: e.target.value as "TEST" | "PRODUCTION" }))}
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
                >
                  <option value="TEST">Test</option>
                  <option value="PRODUCTION">Production</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">Merchant Name</label>
                <input
                  type="text"
                  value={config.googlePayMerchantName}
                  onChange={(e) => setConfig((prev) => ({ ...prev, googlePayMerchantName: e.target.value }))}
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors"
                  placeholder="Your merchant name"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={onGenerateToken}
          disabled={loading || !config.merchantId || !config.description}
          className="bg-gradient-to-r from-slate-800 to-slate-900 text-white py-3 px-6 rounded-lg hover:from-slate-900 hover:to-black disabled:from-slate-400 disabled:to-slate-500 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
        >
          {loading ? "Generating Token..." : "Generate Integration Token"}
        </button>
      </div>
    </div>
  );
};