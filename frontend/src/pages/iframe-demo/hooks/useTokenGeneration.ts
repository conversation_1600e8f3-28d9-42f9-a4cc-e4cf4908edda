import { useState } from "react";
import { toast } from "sonner";
import { generateIntegrationToken } from "../../../services/api";
import { DemoConfig } from "../types";

export const useTokenGeneration = () => {
  const [embedUrl, setEmbedUrl] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const generateToken = async (config: DemoConfig) => {
    setLoading(true);
    setEmbedUrl("");

    try {
      const tokenRequest: Record<string, unknown> = {
        merchantId: config.merchantId,
        description: config.description,
        amount: config.amount,
        returnUrl: config.returnUrl,
        expiresIn: 60,
      };

      if (config.enableGooglePay) {
        tokenRequest.enableDigitalWallets = true;
        tokenRequest.googlePayConfig = {
          enabled: true,
          merchantName: config.googlePayMerchantName || "Auth-Clear",
          environment: config.googlePayEnvironment || "TEST",
          allowedCardNetworks: ["VISA", "MASTERCARD", "AMEX", "DISCOVER"],
          allowedCardAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"],
          billingAddressRequired: true,
          phoneNumberRequired: false,
        };
      }

      const response = await generateIntegrationToken(tokenRequest);

      if (response.success && response.data) {
        setEmbedUrl(response.data.embedUrl);
        toast.success("Integration token generated successfully!");
        return true;
      } else {
        throw new Error("Failed to generate token");
      }
    } catch (error) {
      console.error("Error generating token:", error);
      toast.error("Failed to generate integration token");
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    embedUrl,
    loading,
    generateToken,
  };
};