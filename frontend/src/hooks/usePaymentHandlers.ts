import { useState } from "react";
import { toast } from "sonner";
import { formatSuccessMessage, formatErrorMessage } from "../utils/paymentUtils";
import { PaymentInfo } from "../types/payment";

interface UsePaymentHandlersReturn {
  success: boolean;
  error: string | null;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  handlePaymentSuccess: (response: unknown) => void;
  handlePaymentFailure: (error: unknown) => void;
}

export const usePaymentHandlers = (paymentInfo: PaymentInfo | null): UsePaymentHandlersReturn => {
  const [success, setSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handlePaymentSuccess = (response: unknown) => {
    setSuccess(true);
    toast.success("Payment processed successfully!");

    if (window.parent !== window) {
      window.parent.postMessage(formatSuccessMessage(response, "PAYMENT_SUCCESS"), "*");
    }
    console.log("paymentInfo", paymentInfo);
    // Handle redirect if specified
    // if (paymentInfo?.returnUrl) {
    //   setTimeout(() => {
    //     if (window.parent !== window) {
    //       window.parent.postMessage(
    //         formatSuccessMessage(paymentInfo.returnUrl, "PAYMENT_REDIRECT", {
    //           url: paymentInfo.returnUrl,
    //         }),
    //         "*"
    //       );
    //     } else {
    //       window.location.href = paymentInfo.returnUrl!;
    //     }
    //   }, 2000);
    // }
  };

  const handlePaymentFailure = (error: unknown) => {
    const errorMessage = error instanceof Error ? error.message : (error as { message?: string })?.message || "Payment processing failed";

    setError(errorMessage);
    toast.error(errorMessage);

    // Notify parent window of failure
    if (window.parent !== window) {
      window.parent.postMessage(formatErrorMessage(error, "PAYMENT_FAILURE"), "*");
    }
  };

  return {
    success,
    error,
    setError,
    handlePaymentSuccess,
    handlePaymentFailure,
  };
};
