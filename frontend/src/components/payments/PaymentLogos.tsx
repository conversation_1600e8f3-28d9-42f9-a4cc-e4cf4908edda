import visaLogo from "/paymentLogos/visa.png";
import mastercardLogo from "/paymentLogos/mastercard.png";
import amexLogo from "/paymentLogos/amex.png";
import discoverLogo from "/paymentLogos/discover.png";

interface PaymentLogosProps {
  className?: string;
  showGooglePay?: boolean;
}

const PaymentLogos = ({ className = "", showGooglePay = false }: PaymentLogosProps) => {
  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {/* Google Pay */}
      {showGooglePay && (
        <div className="h-7 bg-white rounded shadow-sm flex items-center justify-center px-2 border border-gray-100">
          <svg width="41" height="17" viewBox="0 0 41 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#gpay-clip)">
              <path
                d="M19.526 8.358V12.83h-1.542V0.938h4.008c.978 0 1.86.39 2.496 1.02a3.45 3.45 0 01.03 4.938c-.63.642-1.518 1.032-2.526 1.032l-2.466.03zm0-5.994v4.578h2.496c.606 0 1.158-.246 1.548-.648.402-.402.636-.954.636-1.56a2.19 2.19 0 00-.636-1.56 2.193 2.193 0 00-1.548-.648l-2.496-.162z"
                fill="#5F6368"
              />
              <path
                d="M24.51 5.63c1.14 0 2.034.33 2.706.*********** 1.566.99 2.7v3.51h-1.47v-.792h-.06c-.66.78-1.38 1.026-2.19 1.026-.78 0-1.434-.234-1.962-.702a2.24 2.24 0 01-.78-1.722c0-.726.276-1.302.816-1.74.54-.438 1.266-.654 2.172-.654.774 0 1.41.138 1.908.426v-.3c0-.456-.18-.852-.534-1.176a1.82 1.82 0 00-1.254-.474c-.774 0-1.386.33-1.836.984l-1.35-.852c.66-1.002 1.71-1.494 3.144-1.494l-.3.18zm-1.674 4.752c0 .342.144.642.426.876.282.234.612.348.996.348.54 0 1.032-.216 1.482-.636.45-.42.666-.906.666-1.458-.39-.312-.936-.468-1.638-.468-.504 0-.93.114-1.272.354-.348.234-.516.564-.516.984h-.144z"
                fill="#5F6368"
              />
              <path d="M34.526 5.858l-4.866 10.89h-1.58l1.806-3.912-3.21-7.008h1.638l2.31 5.394h.03l2.25-5.394 1.622.03z" fill="#5F6368" />
            </g>
            <g clipPath="url(#g-clip)">
              <path
                d="M15.544 8.22c0-.508-.045-1.01-.13-1.5H8v2.837h4.234a3.618 3.618 0 01-1.57 2.373v1.973h2.543c1.487-1.37 2.346-3.387 2.346-5.684l-.01.001z"
                fill="#4285F4"
              />
              <path
                d="M8 15.75c2.124 0 3.905-.705 5.206-1.907l-2.543-1.973c-.704.472-1.605.75-2.663.75-2.048 0-3.783-1.382-4.402-3.24H.966v2.037A7.874 7.874 0 008 15.75z"
                fill="#34A853"
              />
              <path d="M3.598 9.38a4.734 4.734 0 010-3.01V4.333H.966a7.874 7.874 0 000 7.084l2.632-2.037z" fill="#FBBC04" />
              <path
                d="M8 3.13c1.155 0 2.19.397 3.005 1.176l2.253-2.253C12.16.775 10.38 0 8 0A7.874 7.874 0 00.966 4.333L3.598 6.37C4.217 4.512 5.952 3.13 8 3.13z"
                fill="#EA4335"
              />
            </g>
            <defs>
              <clipPath id="gpay-clip">
                <rect width="24" height="17" fill="white" transform="translate(17)" />
              </clipPath>
              <clipPath id="g-clip">
                <rect width="16" height="16" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </div>
      )}

      {/* Visa */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={visaLogo} alt="Visa" className="w-full h-auto object-contain" />
      </div>

      {/* Mastercard */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={mastercardLogo} alt="Mastercard" className="w-full h-auto object-contain" />
      </div>

      {/* American Express */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={amexLogo} alt="American Express" className="w-full h-auto object-contain" />
      </div>

      {/* Discover */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={discoverLogo} alt="Discover" className="w-full h-auto object-contain" />
      </div>
    </div>
  );
};

export default PaymentLogos;
