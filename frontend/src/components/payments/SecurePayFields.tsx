import { useRef, useState } from "react";
import { SecurePayFieldsProps } from "./types/payfields.types";
import { usePayFields } from "./hooks/usePayFields";
import { GooglePayButton } from "./GooglePayButton";
import { toast } from "sonner";

const SecurePayFields = ({ config, paymentInfo, onSuccess, onFailure, className = "", billingAddress }: SecurePayFieldsProps) => {
  const cardNumberRef = useRef<HTMLDivElement>(null);
  const cardNameRef = useRef<HTMLDivElement>(null);
  const cardCvvRef = useRef<HTMLDivElement>(null);
  const cardExpirationRef = useRef<HTMLDivElement>(null);
  const [showCardForm, setShowCardForm] = useState(!config.googlePayConfig?.enabled);

  const { scriptError, isSubmitting, validationError, handleSubmit } = usePayFields({
    config,
    paymentInfo,
    billingAddress,
    onSuccess,
    onFailure,
  });

  const handleGooglePayAuthorized = (paymentData: google.payments.api.PaymentData) => {
    try {
      const token = paymentData.paymentMethodData.tokenizationData.token;
      const paymentResponse = {
        type: "GOOGLE_PAY",
        token,
        paymentMethodData: paymentData.paymentMethodData,
        billingAddress: paymentData.paymentMethodData.info?.billingAddress,
      };

      if (onSuccess) {
        onSuccess(paymentResponse);
      }
      toast.success("Google Pay payment authorized");
    } catch (error) {
      console.error("Error processing Google Pay payment:", error);
      toast.error("Failed to process Google Pay payment");
      if (onFailure) {
        onFailure(error);
      }
    }
  };

  if (scriptError) {
    return (
      <div className={`p-4 bg-red-50 text-red-800 rounded-md ${className}`}>
        <p>{scriptError}</p>
      </div>
    );
  }

  if (!config) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-blue-600 rounded-full mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading secure payment form...</p>
      </div>
    );
  }

  return (
    <div className={`secure-payfields ${className}`}>
      {validationError && (
        <div className="p-4 mb-4 bg-red-50 text-red-800 rounded-md">
          <p>{validationError}</p>
        </div>
      )}

      {config.googlePayConfig?.enabled && (
        <div className="mb-6">
          <div className="flex items-center justify-center mb-4">
            <GooglePayButton
              config={config.googlePayConfig}
              merchantId={config.merchantId}
              amount={paymentInfo?.amount || config.amount}
              onPaymentAuthorized={handleGooglePayAuthorized}
              className="w-full max-w-sm"
            />
          </div>
          {!showCardForm && (
            <div className="text-center">
              <button onClick={() => setShowCardForm(true)} className="text-sm text-blue-600 hover:text-blue-800 underline">
                Pay with card instead
              </button>
            </div>
          )}
          {showCardForm && (
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Or pay with card</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* {showCardForm && ( */}
      <div className="space-y-4">
        <div>
          <label htmlFor="card-number" className="block mb-2 text-sm font-medium text-gray-700">
            Card Number
          </label>
          <div id="card-number" ref={cardNumberRef} className="h-12 border rounded-md"></div>
        </div>

        <div>
          <label htmlFor="card-name" className="block mb-2 text-sm font-medium text-gray-700">
            Cardholder Name
          </label>
          <div id="card-name" ref={cardNameRef} className="h-12 border rounded-md"></div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="card-expiration" className="block mb-2 text-sm font-medium text-gray-700">
              Expiration Date
            </label>
            <div id="card-expiration" ref={cardExpirationRef} className="h-12 border rounded-md"></div>
          </div>
          <div>
            <label htmlFor="card-cvv" className="block mb-2 text-sm font-medium text-gray-700">
              CVV
            </label>
            <div id="card-cvv" ref={cardCvvRef} className="h-12 border rounded-md"></div>
          </div>
        </div>

        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className={`w-full py-3 px-4 font-medium rounded-md transition-all ${
            isSubmitting ? "bg-gray-400 cursor-not-allowed text-white" : "bg-[#364F6B] hover:bg-[#2A3F59] text-white shadow-md hover:shadow-lg"
          }`}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Processing...
            </span>
          ) : (
            `Pay $${((paymentInfo?.amount || config.amount) / 100).toFixed(2)}`
          )}
        </button>
      </div>
      {/* )} */}
    </div>
  );
};

export default SecurePayFields;
