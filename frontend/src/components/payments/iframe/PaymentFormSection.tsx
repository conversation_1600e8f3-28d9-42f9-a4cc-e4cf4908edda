import PaymentLogos from "../PaymentLogos";
import SecurePayFields from "../SecurePayFields";
import type { PayFieldsConfig, BillingAddress, PaymentInfo } from "../../../types/payment";
import { PaymentValidation } from "./PaymentValidation";

interface PaymentFormSectionProps {
  payFieldsConfig: PayFieldsConfig | null;
  paymentInfo: PaymentInfo | null;
  billingAddress: BillingAddress;
  error: string | null;
  isAddressValid: boolean;
  onSuccess: (response: unknown) => void;
  onFailure: (error: unknown) => void;
}

export const PaymentFormSection = ({
  payFieldsConfig,
  paymentInfo,
  billingAddress,
  error,
  isAddressValid,
  onSuccess,
  onFailure,
}: PaymentFormSectionProps) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Information</h2>

      {/* Accepted cards */}
      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-2">Accepted Payment Methods</p>
        <PaymentLogos showGooglePay={payFieldsConfig?.googlePayConfig?.enabled || false} />
      </div>

      <PaymentValidation error={error} isAddressValid={isAddressValid} />

      {payFieldsConfig && (
        <div className={!isAddressValid ? "opacity-50 pointer-events-none" : ""}>
          <SecurePayFields
            config={payFieldsConfig}
            paymentInfo={paymentInfo}
            onSuccess={onSuccess}
            onFailure={onFailure}
            billingAddress={billingAddress}
          />
        </div>
      )}

      {/* Save card option */}
      <div className="mt-4">
        <label className="flex items-center text-sm text-gray-600">
          <input type="checkbox" className="mr-2 h-4 w-4 text-[#364F6B] border-gray-300 rounded focus:ring-[#364F6B]" />
          Save card for future payments
        </label>
      </div>

      {/* Payment note */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>Payment processed securely. Fill in the form and click &quot;Pay Now&quot; to proceed.</p>
      </div>
    </div>
  );
};
