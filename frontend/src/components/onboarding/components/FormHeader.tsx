interface FormHeaderProps {
  title: string;
  subtitle: string;
  onFillDemoData?: () => void;
  showDemoButton?: boolean;
}

export const FormHeader = ({ 
  title, 
  subtitle, 
  onFillDemoData, 
  showDemoButton = true 
}: FormHeaderProps) => {
  return (
    <div className="border-b border-gray-200 px-8 py-6 flex justify-between items-center">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
        <p className="text-gray-600 mt-1">{subtitle}</p>
      </div>
      {showDemoButton && onFillDemoData && (
        <button
          type="button"
          onClick={onFillDemoData}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-colors"
          title="Fill form with demo data for testing"
        >
          <svg 
            className="w-4 h-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M13 10V3L4 14h7v7l9-11h-7z" 
            />
          </svg>
          Fill Demo Data
        </button>
      )}
    </div>
  );
};
