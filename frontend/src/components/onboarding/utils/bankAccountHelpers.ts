import { VERIFICATION_METHODS, type VerificationMethod } from "../constants/bankAccountConstants";
import { PlaidAccountData } from "../../../hooks/usePlaidLink";
import { updateFormData, nextStep } from "../../../redux/slices/onboardingSlice";
import { type AppDispatch } from "../../../redux/store";
import { toast } from "sonner";

export const convertFileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      const base64Content = result.split(",")[1];
      resolve(base64Content);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

export const prepareVerificationData = async (
  verificationMethod: VerificationMethod,
  uploadedFile: File | null,
  plaidAccountData: PlaidAccountData | null
) => {
  const verificationData: {
    verificationMethod: VerificationMethod;
    verificationFile?: {
      name: string;
      size: number;
      type: string;
      content: string;
    };
    plaidData?: {
      publicToken: string;
      accountToken: string;
      platform: "PLAID";
      institutionName?: string;
      accountName?: string;
      accountMask?: string;
    };
  } = {
    verificationMethod,
  };

  if (verificationMethod === VERIFICATION_METHODS.MANUAL && uploadedFile) {
    try {
      const fileContent = await convertFileToBase64(uploadedFile);
      verificationData.verificationFile = {
        name: uploadedFile.name,
        size: uploadedFile.size,
        type: uploadedFile.type,
        content: fileContent,
      };
    } catch (error) {
      console.error("Error converting file to base64:", error);
      toast.error("Failed to process file. Please try again.");
      throw error;
    }
  }

  if (verificationMethod === VERIFICATION_METHODS.PLAID && plaidAccountData) {
    verificationData.plaidData = {
      publicToken: plaidAccountData.publicToken,
      accountToken: plaidAccountData.accountToken,
      platform: "PLAID",
      institutionName: plaidAccountData.institutionName,
      accountName: plaidAccountData.accountName,
      accountMask: plaidAccountData.accountMask,
    };
  }

  return verificationData;
};

export const handleBankAccountSubmit = async (
  e: React.FormEvent,
  validateForm: () => boolean,
  verificationMethod: VerificationMethod,
  uploadedFile: File | null,
  plaidAccountData: PlaidAccountData | null,
  dispatch: AppDispatch
) => {
  e.preventDefault();
  
  if (!validateForm()) {
    return;
  }

  try {
    const verificationData = await prepareVerificationData(
      verificationMethod,
      uploadedFile,
      plaidAccountData
    );
    
    dispatch(updateFormData({ bankVerification: verificationData }));
    dispatch(nextStep());
  } catch {
    // Error already handled in prepareVerificationData
    return;
  }
};