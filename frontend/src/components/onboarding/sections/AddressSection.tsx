import { TextInput, SelectInput } from "../form-fields";
import { US_STATES } from "../constants/businessConstants";
import { ValidationErrors, FormData } from "../utils/validation";

interface AddressSectionProps {
  formData: FormData;
  errors: ValidationErrors;
  onChange: (field: string, value: string) => void;
}

export const AddressSection = ({ formData, errors, onChange }: AddressSectionProps) => {
  const stateOptions = US_STATES.map((state) => ({ value: state, label: state }));

  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Business Address</h2>
      <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <div className="flex">
          <div className="ml-3">
            <p className="text-sm text-red-800">
              <strong>Info:</strong> PO Boxes are not acceptable for onboarding
            </p>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TextInput
          label="Street Address"
          value={formData.address1 || ""}
          onChange={(value) => onChange("address1", value)}
          error={errors.address1}
          required
          placeholder="123 Business Street"
          className="lg:col-span-2"
        />

        <TextInput
          label="Address Line 2"
          value={formData.address2 || ""}
          onChange={(value) => onChange("address2", value)}
          placeholder="Suite, floor, etc. (optional)"
          className="lg:col-span-2"
        />

        <TextInput
          label="City"
          value={formData.city || ""}
          onChange={(value) => onChange("city", value)}
          error={errors.city}
          required
          placeholder="Los Angeles"
        />

        <SelectInput
          label="State"
          value={formData.state || ""}
          onChange={(value) => onChange("state", value as string)}
          options={stateOptions}
          error={errors.state}
          required
          placeholder="Select state"
        />

        <TextInput
          label="ZIP Code"
          value={formData.zip || ""}
          onChange={(value) => onChange("zip", value)}
          error={errors.zip}
          required
          placeholder="90210"
        />

        <TextInput label="Country" value={formData.country || "USA"} onChange={(value) => onChange("country", value)} placeholder="USA" />
      </div>
    </div>
  );
};
