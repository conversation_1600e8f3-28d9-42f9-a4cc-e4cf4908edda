import { TextInput, SelectInput } from "../../form-fields";
import { Member } from "../../constants/ownerConstants";
import { US_STATES } from "../../constants/businessConstants";

interface OwnerAddressFieldsProps {
  member: Member;
  index: number;
  onFieldChange: (index: number, field: string, value: string | number) => void;
  errors: Record<string, string>;
}

export const OwnerAddressFields = ({ member, index, onFieldChange, errors }: OwnerAddressFieldsProps) => {
  const stateOptions = US_STATES.map((state) => ({ value: state, label: state }));

  return (
    <div className="mb-8">
      <h3 className="text-md font-medium text-gray-900 mb-4">Personal Address</h3>
      <p className="text-gray-600 mb-4">Enter home address (not business address)</p>
      <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <div className="flex">
          <div className="ml-3">
            <p className="text-sm text-red-800">
              <strong>Info:</strong> PO Boxes are not acceptable for onboarding
            </p>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="lg:col-span-2">
          <TextInput
            label="Street Address"
            value={member.address1 || ""}
            onChange={(value) => onFieldChange(index, "address1", value)}
            placeholder="123 Home Street"
            error={errors[`member${index}.address1`]}
            required
          />
        </div>

        <div className="lg:col-span-2">
          <TextInput
            label="Address Line 2"
            value={member.address2 || ""}
            onChange={(value) => onFieldChange(index, "address2", value)}
            placeholder="Apt, suite, etc. (optional)"
          />
        </div>

        <TextInput
          label="City"
          value={member.city || ""}
          onChange={(value) => onFieldChange(index, "city", value)}
          placeholder="Los Angeles"
          error={errors[`member${index}.city`]}
          required
        />

        <SelectInput
          label="State"
          value={member.state || ""}
          onChange={(value) => onFieldChange(index, "state", value as string)}
          options={stateOptions}
          placeholder="Select state"
          error={errors[`member${index}.state`]}
          required
        />

        <TextInput
          label="ZIP Code"
          value={member.zip || ""}
          onChange={(value) => onFieldChange(index, "zip", value)}
          placeholder="90210"
          error={errors[`member${index}.zip`]}
          required
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
          <input type="text" value="USA" readOnly className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50" />
        </div>
      </div>
    </div>
  );
};
