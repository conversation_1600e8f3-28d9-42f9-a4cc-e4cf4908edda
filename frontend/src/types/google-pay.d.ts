declare namespace google {
  namespace payments {
    namespace api {
      interface PaymentsClient {
        isReadyToPay(request: IsReadyToPayRequest): Promise<IsReadyToPayResponse>;
        loadPaymentData(request: PaymentDataRequest): Promise<PaymentData>;
        createButton(options: ButtonOptions): HTMLElement;
      }

      interface PaymentsClientConfig {
        environment: string;
      }

      interface IsReadyToPayRequest {
        apiVersion: number;
        apiVersionMinor: number;
        allowedPaymentMethods: PaymentMethod[];
      }

      interface IsReadyToPayResponse {
        result: boolean;
      }

      interface PaymentDataRequest {
        apiVersion: number;
        apiVersionMinor: number;
        allowedPaymentMethods: PaymentMethod[];
        merchantInfo: MerchantInfo;
        transactionInfo: TransactionInfo;
        shippingAddressRequired?: boolean;
        shippingAddressParameters?: ShippingAddressParameters;
      }

      interface PaymentMethod {
        type: string;
        parameters: CardParameters;
        tokenizationSpecification: PaymentMethodTokenizationSpecification;
      }

      interface CardParameters {
        allowedAuthMethods: string[];
        allowedCardNetworks: string[];
        billingAddressRequired?: boolean;
        billingAddressParameters?: BillingAddressParameters;
      }

      interface BillingAddressParameters {
        format: string;
        phoneNumberRequired?: boolean;
      }

      interface PaymentMethodTokenizationSpecification {
        type: string;
        parameters: {
          gateway: string;
          gatewayMerchantId: string;
        };
      }

      interface MerchantInfo {
        merchantName: string;
        merchantId?: string;
      }

      interface TransactionInfo {
        totalPriceStatus: string;
        totalPrice: string;
        currencyCode: string;
        countryCode?: string;
      }

      interface ShippingAddressParameters {
        phoneNumberRequired?: boolean;
      }

      interface PaymentData {
        paymentMethodData: PaymentMethodData;
      }

      interface PaymentMethodData {
        type: string;
        description?: string;
        info?: {
          billingAddress?: BillingAddress;
        };
        tokenizationData: {
          type: string;
          token: string;
        };
      }

      interface BillingAddress {
        name?: string;
        postalCode?: string;
        countryCode?: string;
        phoneNumber?: string;
        address1?: string;
        address2?: string;
        address3?: string;
        locality?: string;
        administrativeArea?: string;
        sortingCode?: string;
      }

      interface ButtonOptions {
        onClick: () => void;
        buttonType?: string;
        buttonColor?: string;
        buttonSizeMode?: string;
      }
    }
  }
}