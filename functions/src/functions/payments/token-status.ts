import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createIframeResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { validateToken } from "./generate-integration-token.js";
import { withIframeSecurity, validateTokenFormat } from "../../middleware/security.js";
import { z } from "zod";

interface TokenStatusResponse {
  isValid: boolean;
  status: "valid" | "expired" | "used" | "invalid";
  expiresAt?: string;
  timeRemaining?: number;
  merchantId?: string;
  amount?: number;
  description?: string;
}

const tokenStatusSchema = z.object({
  token: z.string().min(1, "Token is required"),
});

const handlerImpl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    let token: string;
    if (event.httpMethod === "GET") {
      token = event.queryStringParameters?.token || "";
    } else {
      if (!event.body) {
        return createIframeResponse(400, {
          error: "Request body is required for POST requests",
          message: "Please provide a token",
        });
      }

      try {
        const parsedBody = JSON.parse(event.body);
        const requestData = tokenStatusSchema.parse(parsedBody);
        token = requestData.token;
      } catch (error) {
        if (error instanceof z.ZodError) {
          return createIframeResponse(400, {
            error: "Validation failed",
            message: "Invalid request data",
            details: error.errors,
          });
        }
        throw error;
      }
    }

    const tokenFormatValidation = validateTokenFormat(token);
    if (!tokenFormatValidation.isValid) {
      return createIframeResponse(tokenFormatValidation.statusCode || 400, {
        error: tokenFormatValidation.error,
        message: "Invalid token format",
      });
    }

    const tokenValidation = await validateToken(token);

    let response: TokenStatusResponse;

    if (tokenValidation.isValid && tokenValidation.data) {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 60 * 60 * 1000);
      const timeRemaining = Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / 1000));

      response = {
        isValid: true,
        status: "valid",
        expiresAt: expiresAt.toISOString(),
        timeRemaining,
        merchantId: tokenValidation.data.merchantId,
        amount: tokenValidation.data.amount,
        description: tokenValidation.data.description,
      };
    } else {
      let status: "expired" | "used" | "invalid" = "invalid";

      if (tokenValidation.error?.includes("expired")) {
        status = "expired";
      } else if (tokenValidation.error?.includes("used")) {
        status = "used";
      }

      response = {
        isValid: false,
        status,
      };
    }

    return createIframeResponse(200, {
      success: true,
      message: "Token status retrieved successfully",
      data: response,
    });
  } catch (error) {
    logger.error("Error checking token status", { error });

    return createIframeResponse(500, {
      error: "Internal server error",
      message: "Failed to check token status",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const handler = withIframeSecurity(handlerImpl);
