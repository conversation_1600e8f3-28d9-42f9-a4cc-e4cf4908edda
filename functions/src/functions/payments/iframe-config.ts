import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { createIframeResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { withIframeSecurity } from "../../middleware/security.js";

interface IframeConfigRequest {
  domain?: string;
  theme?: "light" | "dark" | "auto";
  language?: string;
  currency?: string;
}

interface IframeConfigResponse {
  payrixConfig: {
    scriptUrl: string;
    environment: string;
    supportedFeatures: string[];
  };
  styling: {
    theme: string;
    customCSS: Record<string, string>;
  };
  security: {
    allowedOrigins: string[];
    cspDirectives: string[];
  };
  features: {
    autoResize: boolean;
    responsiveDesign: boolean;
    mobileOptimized: boolean;
  };
}

const handlerImpl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    let configRequest: IframeConfigRequest = {};

    if (event.queryStringParameters) {
      configRequest = {
        domain: event.queryStringParameters.domain || undefined,
        theme: (event.queryStringParameters.theme as "light" | "dark" | "auto") || "light",
        language: event.queryStringParameters.language || "en",
        currency: event.queryStringParameters.currency || "USD",
      };
    }

    if (event.body) {
      try {
        const bodyData = JSON.parse(event.body);
        configRequest = { ...configRequest, ...bodyData };
      } catch (error) {
        logger.error("Failed to parse request body", { error });
      }
    }

    const { domain, theme = "light", language = "en", currency = "USD" } = configRequest;

    const environment = process.env.PAYRIX_ENVIRONMENT || "test";
    const payrixScriptUrl = environment === "production" ? "https://api.payrix.com/payFieldsScript" : "https://test-api.payrix.com/payFieldsScript";

    const themeStyles = generateThemeStyles(theme);

    const allowedOrigins = domain ? [domain] : ["*"];
    const cspDirectives = [
      "frame-ancestors *",
      `script-src 'self' 'unsafe-inline' ${payrixScriptUrl.replace("/payFieldsScript", "")}`,
      `connect-src 'self' ${payrixScriptUrl.replace("/payFieldsScript", "")}`,
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
    ];

    const response: IframeConfigResponse = {
      payrixConfig: {
        scriptUrl: `${payrixScriptUrl}?spa=1&iframe=1&lang=${language}&currency=${currency}`,
        environment,
        supportedFeatures: [
          "iframe-embedding",
          "auto-resize",
          "responsive-design",
          "mobile-optimization",
          "real-time-validation",
          "secure-tokenization",
        ],
      },
      styling: {
        theme,
        customCSS: themeStyles,
      },
      security: {
        allowedOrigins,
        cspDirectives,
      },
      features: {
        autoResize: true,
        responsiveDesign: true,
        mobileOptimized: true,
      },
    };

    return createIframeResponse(200, {
      success: true,
      message: "Iframe configuration generated successfully",
      data: response,
    });
  } catch (error) {
    logger.error("Error generating iframe configuration", { error });

    return createIframeResponse(500, {
      error: "Internal server error",
      message: "Failed to generate iframe configuration",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

function generateThemeStyles(theme: string): Record<string, string> {
  const baseStyles = {
    ".payment-container": `
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.5;
      color: #374151;
    `,
    ".payment-form": `
      max-width: 400px;
      margin: 0 auto;
      padding: 1.5rem;
    `,
    ".form-group": `
      margin-bottom: 1rem;
    `,
    ".form-label": `
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      font-size: 0.875rem;
    `,
  };

  const themeSpecificStyles = {
    light: {
      ".payment-container":
        baseStyles[".payment-container"] +
        `
        background-color: #ffffff;
      `,
      ".form-label":
        baseStyles[".form-label"] +
        `
        color: #374151;
      `,
      ".payment-button": `
        background-color: #3b82f6;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      `,
      ".payment-button:hover": `
        background-color: #2563eb;
      `,
      ".payment-button:disabled": `
        background-color: #9ca3af;
        cursor: not-allowed;
      `,
    },
    dark: {
      ".payment-container":
        baseStyles[".payment-container"] +
        `
        background-color: #1f2937;
        color: #f9fafb;
      `,
      ".form-label":
        baseStyles[".form-label"] +
        `
        color: #f9fafb;
      `,
      ".payment-button": `
        background-color: #4f46e5;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      `,
      ".payment-button:hover": `
        background-color: #4338ca;
      `,
      ".payment-button:disabled": `
        background-color: #6b7280;
        cursor: not-allowed;
      `,
    },
  };

  return {
    ...baseStyles,
    ...(themeSpecificStyles[theme as keyof typeof themeSpecificStyles] || themeSpecificStyles.light),
  };
}

export const handler = withIframeSecurity(handlerImpl);
