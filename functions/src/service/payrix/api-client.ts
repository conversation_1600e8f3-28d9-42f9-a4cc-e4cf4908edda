import axios, { AxiosInstance } from "axios";

const PAYRIX_API_URL = process.env.PAYRIX_API_URL || "https://test-api.payrix.com";
const PAYRIX_PRIVATE_API_KEY = process.env.PAYRIX_PRIVATE_API_KEY || "c941c2f0f23137ba6ae868662e8d0bf5";
const PAYRIX_CREDENTIAL_ID = process.env.PAYRIX_CREDENTIAL_ID;

export function createPayrixApiClient(): AxiosInstance {
  return axios.create({
    baseURL: PAYRIX_API_URL,
    headers: {
      "Content-Type": "application/json",
      APIKEY: PAYRIX_PRIVATE_API_KEY,
    },
  });
}

export { PAYRIX_API_URL, PAYRIX_PRIVATE_API_KEY, PAYRIX_CREDENTIAL_ID };
